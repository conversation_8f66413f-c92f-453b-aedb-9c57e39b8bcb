-- ODude Assets System Database Schema
-- This script creates all required tables for the assets management system
--
-- Features included:
-- - Asset creation and management
-- - Asset transfers between ODude names
-- - Asset approval/decline system
-- - Public asset showcase
-- - Asset templates and metadata

-- Create the assets table
CREATE TABLE IF NOT EXISTS assets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  description TEXT,
  asset_type TEXT NOT NULL CHECK (asset_type IN ('Badge', 'Certificate', 'Ticket', 'Coupon')),
  image_url TEXT NOT NULL,
  template_id TEXT,
  issuer_odude_name TEXT NOT NULL,
  issuer_email TEXT NOT NULL,
  expiry_date TIMESTAMPTZ,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  is_deleted BOOLEAN DEFAULT FALSE
);

-- Create the asset_transfers table (tracks all asset sends/receives)
CREATE TABLE IF NOT EXISTS asset_transfers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  asset_id UUID NOT NULL REFERENCES assets(id) ON DELETE CASCADE,
  from_odude_name TEXT NOT NULL,
  from_email TEXT NOT NULL,
  to_odude_name TEXT NOT NULL,
  to_email TEXT,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'declined', 'hidden')),
  transferred_at TIMESTAMPTZ DEFAULT NOW(),
  responded_at TIMESTAMPTZ,
  response_note TEXT
);

-- Create the asset_templates table (predefined templates)
CREATE TABLE IF NOT EXISTS asset_templates (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  asset_type TEXT NOT NULL CHECK (asset_type IN ('Badge', 'Certificate', 'Ticket', 'Coupon')),
  template_data JSONB NOT NULL DEFAULT '{}',
  preview_url TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_assets_issuer_email ON assets(issuer_email);
CREATE INDEX IF NOT EXISTS idx_assets_issuer_odude_name ON assets(issuer_odude_name);
CREATE INDEX IF NOT EXISTS idx_assets_type ON assets(asset_type);
CREATE INDEX IF NOT EXISTS idx_assets_created_at ON assets(created_at);
CREATE INDEX IF NOT EXISTS idx_assets_expiry_date ON assets(expiry_date);
CREATE INDEX IF NOT EXISTS idx_assets_deleted ON assets(is_deleted);

CREATE INDEX IF NOT EXISTS idx_asset_transfers_asset_id ON asset_transfers(asset_id);
CREATE INDEX IF NOT EXISTS idx_asset_transfers_to_odude_name ON asset_transfers(to_odude_name);
CREATE INDEX IF NOT EXISTS idx_asset_transfers_to_email ON asset_transfers(to_email);
CREATE INDEX IF NOT EXISTS idx_asset_transfers_from_email ON asset_transfers(from_email);
CREATE INDEX IF NOT EXISTS idx_asset_transfers_status ON asset_transfers(status);
CREATE INDEX IF NOT EXISTS idx_asset_transfers_transferred_at ON asset_transfers(transferred_at);

CREATE INDEX IF NOT EXISTS idx_asset_templates_type ON asset_templates(asset_type);
CREATE INDEX IF NOT EXISTS idx_asset_templates_active ON asset_templates(is_active);

-- Add GIN indexes for JSONB columns
CREATE INDEX IF NOT EXISTS idx_assets_metadata ON assets USING GIN (metadata);
CREATE INDEX IF NOT EXISTS idx_asset_templates_data ON asset_templates USING GIN (template_data);

-- Add comments for documentation
COMMENT ON TABLE assets IS 'Main assets table storing all created assets with metadata';
COMMENT ON TABLE asset_transfers IS 'Tracks asset transfers between ODude names with approval status';
COMMENT ON TABLE asset_templates IS 'Predefined templates for asset creation';

COMMENT ON COLUMN assets.asset_type IS 'Type of asset: Badge, Certificate, Ticket, or Coupon';
COMMENT ON COLUMN assets.metadata IS 'JSON object containing additional asset metadata';
COMMENT ON COLUMN assets.template_id IS 'Reference to asset_templates.id if created from template';
COMMENT ON COLUMN assets.is_deleted IS 'Soft delete flag - when true, asset is deleted everywhere';

COMMENT ON COLUMN asset_transfers.status IS 'Transfer status: pending, approved, declined, or hidden';
COMMENT ON COLUMN asset_transfers.to_email IS 'Recipient email - can be null if ODude name not registered';

-- Insert default templates
INSERT INTO asset_templates (id, name, description, asset_type, template_data, preview_url) VALUES
('badge_default', 'Default Badge', 'Simple badge template', 'Badge', '{"background": "#4F46E5", "border": "rounded", "icon": "star"}', '/templates/badge_default.svg'),
('certificate_default', 'Default Certificate', 'Professional certificate template', 'Certificate', '{"background": "#FFFFFF", "border": "formal", "seal": true}', '/templates/certificate_default.svg'),
('ticket_default', 'Default Ticket', 'Event ticket template', 'Ticket', '{"background": "#F59E0B", "border": "dashed", "qr": true}', '/templates/ticket_default.svg'),
('coupon_default', 'Default Coupon', 'Discount coupon template', 'Coupon', '{"background": "#EF4444", "border": "wavy", "discount": true}', '/templates/coupon_default.svg')
ON CONFLICT (id) DO NOTHING;

-- Create a view for active assets with transfer counts
CREATE OR REPLACE VIEW assets_with_stats AS
SELECT 
  a.*,
  COUNT(at.id) as total_transfers,
  COUNT(CASE WHEN at.status = 'approved' THEN 1 END) as approved_transfers,
  COUNT(CASE WHEN at.status = 'pending' THEN 1 END) as pending_transfers
FROM assets a
LEFT JOIN asset_transfers at ON a.id = at.asset_id
WHERE a.is_deleted = FALSE
GROUP BY a.id;

-- Verification queries
SELECT 'Assets system tables created successfully' as status;
SELECT COUNT(*) as total_assets FROM assets;
SELECT COUNT(*) as total_transfers FROM asset_transfers;
SELECT COUNT(*) as total_templates FROM asset_templates;
